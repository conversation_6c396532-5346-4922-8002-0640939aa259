
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_Diamonds {
    public IAP_Diamonds(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Products").getAsJsonArray(); Products = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Products.add(_v0); }   }
        Parameters = _buf.get("Parameters").getAsString();
    }

    public static IAP_Diamonds deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_Diamonds(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 当即奖励<br/>itemid,count,showCount|itemid,count,,showCount
     */
    public final java.util.ArrayList<String> Products;
    /**
     * 参数
     */
    public final String Parameters;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "Products:" + Products + ","
        + "Parameters:" + Parameters + ","
        + "}";
    }
}

