
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Item_dropModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Item_drop> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.Item_drop> _dataList;
    
    public Item_dropModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Item_drop>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Item_drop>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Item_drop _v;
            _v = com.dxx.game.config.Bean.Item_drop.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.drop_id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Item_drop> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.Item_drop> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Item_drop get(int key) { return _dataMap.get(key); }

}
