
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_GiftPacks {
    public IAP_GiftPacks(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        PackType = _buf.get("PackType").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Products").getAsJsonArray(); Products = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Products.add(_v0); }   }
        PurchaseCount = _buf.get("PurchaseCount").getAsInt();
    }

    public static IAP_GiftPacks deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_GiftPacks(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 刷新类型<br/>（类型1:每天<br/>类型2:周一<br/>类型3：每月1日）
     */
    public final int PackType;
    /**
     * 当即奖励<br/>itemid,count,showCount|itemid,count,,showCount
     */
    public final java.util.ArrayList<String> Products;
    /**
     * 最大购买次数<br/>0:无条件<br/>&gt;0:最大次数
     */
    public final int PurchaseCount;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "PackType:" + PackType + ","
        + "Products:" + Products + ","
        + "PurchaseCount:" + PurchaseCount + ","
        + "}";
    }
}

