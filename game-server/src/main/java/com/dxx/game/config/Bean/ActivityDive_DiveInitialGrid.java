
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityDive_DiveInitialGrid {
    public ActivityDive_DiveInitialGrid(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Grid").getAsJsonArray(); Grid = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Grid.add(_v0); }   }
    }

    public static ActivityDive_DiveInitialGrid deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 初始格子的配置（初始格子不刷奖励）<br/>默认五行，从左往右从上到下配置<br/>0为空白格，1为气泡格，2为冰块格
     */
    public final java.util.ArrayList<Integer> Grid;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Grid:" + Grid + ","
        + "}";
    }
}

