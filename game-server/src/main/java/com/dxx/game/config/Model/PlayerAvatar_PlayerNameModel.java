
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class PlayerAvatar_PlayerNameModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.PlayerAvatar_PlayerName> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.PlayerAvatar_PlayerName> _dataList;
    
    public PlayerAvatar_PlayerNameModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.PlayerAvatar_PlayerName>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.PlayerAvatar_PlayerName>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.PlayerAvatar_PlayerName _v;
            _v = com.dxx.game.config.Bean.PlayerAvatar_PlayerName.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.Id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.PlayerAvatar_PlayerName> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.PlayerAvatar_PlayerName> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.PlayerAvatar_PlayerName get(int key) { return _dataMap.get(key); }

}
