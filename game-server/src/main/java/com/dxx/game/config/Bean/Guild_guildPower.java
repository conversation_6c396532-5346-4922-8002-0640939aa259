
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildPower {
    public Guild_guildPower(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Notes = _buf.get("Notes").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("Power").getAsJsonArray(); Power = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Power.add(_v0); }   }
    }

    public static Guild_guildPower deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildPower(_buf);
    }

    /**
     * Id
     */
    public final int ID;
    /**
     * 备注
     */
    public final String Notes;
    /**
     * 权限<br/>1.审批加入<br/>2.修改公会信息与入会要求<br/>3.活动相关<br/>4.任命/取消管理<br/>5.任命/取消副会长<br/>6.解散公会<br/>7.踢人<br/>8.升级公会<br/>
     */
    public final java.util.ArrayList<Integer> Power;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Notes:" + Notes + ","
        + "Power:" + Power + ","
        + "}";
    }
}

