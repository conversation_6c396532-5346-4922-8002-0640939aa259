
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityDive_DiveInsertGrid {
    public ActivityDive_DiveInsertGrid(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Type = _buf.get("Type").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Grid").getAsJsonArray(); Grid = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Grid.add(_v0); }   }
    }

    public static ActivityDive_DiveInsertGrid deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityDive_DiveInsertGrid(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 地图类型<br/>1：通用地图<br/>2：补强库（深度太少了走这个）<br/>3：补弱库（深度太多了走这个）
     */
    public final int Type;
    /**
     * 中插格子的配置（无空白格）<br/>默认一行，从左往右配置<br/>1为气泡格，2为冰块格
     */
    public final java.util.ArrayList<Integer> Grid;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Type:" + Type + ","
        + "Grid:" + Grid + ","
        + "}";
    }
}

