
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class SevenDay_SevenDayActiveRewardModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward> _dataList;
    
    public SevenDay_SevenDayActiveRewardModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward _v;
            _v = com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward get(int key) { return _dataMap.get(key); }

}
