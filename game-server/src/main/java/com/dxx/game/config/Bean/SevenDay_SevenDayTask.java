
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class SevenDay_SevenDayTask {
    public SevenDay_SevenDayTask(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Day = _buf.get("Day").getAsInt();
        TaskType = _buf.get("TaskType").getAsInt();
        StatisticsType = _buf.get("StatisticsType").getAsInt();
        Need = _buf.get("Need").getAsInt();
        Describe = _buf.get("Describe").getAsString();
        ProgressType = _buf.get("ProgressType").getAsInt();
        Active = _buf.get("Active").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        Jump = _buf.get("Jump").getAsInt();
        UnlockNeed = _buf.get("UnlockNeed").getAsInt();
    }

    public static SevenDay_SevenDayTask deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.SevenDay_SevenDayTask(_buf);
    }

    /**
     * ID<br/>天数1位+类型3位+顺序2位
     */
    public final int ID;
    /**
     * 第几天
     */
    public final int Day;
    /**
     * 任务类型<br/><br/>
     */
    public final int TaskType;
    /**
     * 统计类型<br/>0,非累加<br/>1,累加
     */
    public final int StatisticsType;
    /**
     * 完成要求
     */
    public final int Need;
    /**
     * 多语言ID
     */
    public final String Describe;
    /**
     * 0:显示0/1<br/>1:直接显示配置数
     */
    public final int ProgressType;
    /**
     * 任务完成领取的活跃度
     */
    public final int Active;
    /**
     * 奖励领取
     */
    public final java.util.ArrayList<String> Reward;
    /**
     * 跳转
     */
    public final int Jump;
    /**
     * Function中ID<br/>0为默认解锁<br/>-1为不开放
     */
    public final int UnlockNeed;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Day:" + Day + ","
        + "TaskType:" + TaskType + ","
        + "StatisticsType:" + StatisticsType + ","
        + "Need:" + Need + ","
        + "Describe:" + Describe + ","
        + "ProgressType:" + ProgressType + ","
        + "Active:" + Active + ","
        + "Reward:" + Reward + ","
        + "Jump:" + Jump + ","
        + "UnlockNeed:" + UnlockNeed + ","
        + "}";
    }
}

