
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class SignIn_SignIn {
    public SignIn_SignIn(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        MinDays = _buf.get("MinDays").getAsInt();
        MaxDays = _buf.get("MaxDays").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Day1").getAsJsonArray(); Day1 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Day1.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Day2").getAsJsonArray(); Day2 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Day2.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Day3").getAsJsonArray(); Day3 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Day3.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Day4").getAsJsonArray(); Day4 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Day4.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Day5").getAsJsonArray(); Day5 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Day5.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Day6").getAsJsonArray(); Day6 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Day6.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Day7").getAsJsonArray(); Day7 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Day7.add(_v0); }   }
        colour = _buf.get("colour").getAsString();
    }

    public static SignIn_SignIn deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.SignIn_SignIn(_buf);
    }

    /**
     * id
     */
    public final int ID;
    /**
     * 最小签到天数<br/>(根据用户的签到天数查询不同的签到奖励)
     */
    public final int MinDays;
    /**
     * 最大签到天数
     */
    public final int MaxDays;
    /**
     * 第一天奖励
     */
    public final java.util.ArrayList<String> Day1;
    /**
     * 第二天奖励
     */
    public final java.util.ArrayList<String> Day2;
    /**
     * 第三天奖励
     */
    public final java.util.ArrayList<String> Day3;
    /**
     * 第四天奖励
     */
    public final java.util.ArrayList<String> Day4;
    /**
     * 第五天奖励
     */
    public final java.util.ArrayList<String> Day5;
    /**
     * 第六天奖励
     */
    public final java.util.ArrayList<String> Day6;
    /**
     * 第七天奖励
     */
    public final java.util.ArrayList<String> Day7;
    /**
     * 哪些天数着重显示
     */
    public final String colour;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "MinDays:" + MinDays + ","
        + "MaxDays:" + MaxDays + ","
        + "Day1:" + Day1 + ","
        + "Day2:" + Day2 + ","
        + "Day3:" + Day3 + ","
        + "Day4:" + Day4 + ","
        + "Day5:" + Day5 + ","
        + "Day6:" + Day6 + ","
        + "Day7:" + Day7 + ","
        + "colour:" + colour + ","
        + "}";
    }
}

