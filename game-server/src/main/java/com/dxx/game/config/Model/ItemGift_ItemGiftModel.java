
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class ItemGift_ItemGiftModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.ItemGift_ItemGift> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.ItemGift_ItemGift> _dataList;
    
    public ItemGift_ItemGiftModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.ItemGift_ItemGift>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.ItemGift_ItemGift>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.ItemGift_ItemGift _v;
            _v = com.dxx.game.config.Bean.ItemGift_ItemGift.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.ItemGift_ItemGift> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.ItemGift_ItemGift> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.ItemGift_ItemGift get(int key) { return _dataMap.get(key); }

}
