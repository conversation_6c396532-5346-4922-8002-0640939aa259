
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Book_Book {
    public Book_Book(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        startPoint = _buf.get("startPoint").getAsInt();
        stage = _buf.get("stage").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("rewards").getAsJsonArray(); rewards = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  rewards.add(_v0); }   }
    }

    public static Book_Book deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Book_Book(_buf);
    }

    /**
     * 图鉴等级
     */
    public final int ID;
    /**
     * 开始点数
     */
    public final int startPoint;
    /**
     * 阶段点数
     */
    public final int stage;
    /**
     * 奖励
     */
    public final java.util.ArrayList<String> rewards;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "startPoint:" + startPoint + ","
        + "stage:" + stage + ","
        + "rewards:" + rewards + ","
        + "}";
    }
}

