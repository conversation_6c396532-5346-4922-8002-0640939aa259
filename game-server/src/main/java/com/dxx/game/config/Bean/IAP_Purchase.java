
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_Purchase {
    public IAP_Purchase(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        notes = _buf.get("notes").getAsString();
        TitleID = _buf.get("TitleID").getAsString();
        NameID = _buf.get("NameID").getAsString();
        DescID = _buf.get("DescID").getAsString();
        TwoLinesID = _buf.get("TwoLinesID").getAsInt();
        DiscountID = _buf.get("DiscountID").getAsString();
        IconAtlasID = _buf.get("IconAtlasID").getAsInt();
        IconName = _buf.get("IconName").getAsString();
        ProductType = _buf.get("ProductType").getAsInt();
        IAPID = _buf.get("IAPID").getAsString();
        Price = _buf.get("Price").getAsFloat();
        ChinaPrice = _buf.get("ChinaPrice").getAsFloat();
        VipExp = _buf.get("VipExp").getAsInt();
        LimitCount = _buf.get("LimitCount").getAsInt();
        Priority = _buf.get("Priority").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("ShowProducts").getAsJsonArray(); ShowProducts = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  ShowProducts.add(_v0); }   }
    }

    public static IAP_Purchase deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_Purchase(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 阐述
     */
    public final String notes;
    /**
     * 标题ID
     */
    public final String TitleID;
    /**
     * 名称
     */
    public final String NameID;
    /**
     * 描述
     */
    public final String DescID;
    /**
     * 两行描述带图标
     */
    public final int TwoLinesID;
    /**
     * 折扣ID
     */
    public final String DiscountID;
    /**
     * icon图集
     */
    public final int IconAtlasID;
    /**
     * icon名称
     */
    public final String IconName;
    /**
     * 商店类型<br/>1、商店-钻石<br/>2、商店-礼包<br/>3、月卡<br/>4、通行证<br/>5、关卡基金<br/>6、推送礼包
     */
    public final int ProductType;
    /**
     * IAP页签ID
     */
    public final String IAPID;
    /**
     * 价格<br/>真实货币单位为美元
     */
    public final float Price;
    /**
     * 价格<br/>真实货币单位为人民币
     */
    public final float ChinaPrice;
    /**
     * 购买获得的vip积分
     */
    public final int VipExp;
    /**
     * 最大购买次数<br/>0:无条件<br/>&gt;0:最大次数
     */
    public final int LimitCount;
    /**
     * 品类相同的显示优先级
     */
    public final int Priority;
    /**
     * 展示奖励<br/>itemid,count,showCount|itemid,count,,showCount
     */
    public final java.util.ArrayList<String> ShowProducts;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "notes:" + notes + ","
        + "TitleID:" + TitleID + ","
        + "NameID:" + NameID + ","
        + "DescID:" + DescID + ","
        + "TwoLinesID:" + TwoLinesID + ","
        + "DiscountID:" + DiscountID + ","
        + "IconAtlasID:" + IconAtlasID + ","
        + "IconName:" + IconName + ","
        + "ProductType:" + ProductType + ","
        + "IAPID:" + IAPID + ","
        + "Price:" + Price + ","
        + "ChinaPrice:" + ChinaPrice + ","
        + "VipExp:" + VipExp + ","
        + "LimitCount:" + LimitCount + ","
        + "Priority:" + Priority + ","
        + "ShowProducts:" + ShowProducts + ","
        + "}";
    }
}

