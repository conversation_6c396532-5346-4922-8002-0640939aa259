
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ServerList_serverGroup {
    public ServerList_serverGroup(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("range").getAsJsonArray(); range = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  range.add(_v0); }   }
        groupName = _buf.get("groupName").getAsString();
    }

    public static ServerList_serverGroup deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ServerList_serverGroup(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 范围
     */
    public final java.util.ArrayList<Integer> range;
    /**
     * 标记
     */
    public final String groupName;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "range:" + range + ","
        + "groupName:" + groupName + ","
        + "}";
    }
}

