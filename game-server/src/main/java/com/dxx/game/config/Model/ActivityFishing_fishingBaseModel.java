
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class ActivityFishing_fishingBaseModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityFishing_fishingBase> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.ActivityFishing_fishingBase> _dataList;
    
    public ActivityFishing_fishingBaseModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityFishing_fishingBase>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.ActivityFishing_fishingBase>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.ActivityFishing_fishingBase _v;
            _v = com.dxx.game.config.Bean.ActivityFishing_fishingBase.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityFishing_fishingBase> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.ActivityFishing_fishingBase> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.ActivityFishing_fishingBase get(int key) { return _dataMap.get(key); }

}
