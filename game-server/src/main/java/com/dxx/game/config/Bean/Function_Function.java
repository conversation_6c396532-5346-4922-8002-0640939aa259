
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Function_Function {
    public Function_Function(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        unlockType = _buf.get("unlockType").getAsInt();
        unlockArgs = _buf.get("unlockArgs").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("afterBattleMutex").getAsJsonArray(); afterBattleMutex = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  afterBattleMutex.add(_v0); }   }
        nameID = _buf.get("nameID").getAsString();
        desID = _buf.get("desID").getAsString();
        showView = _buf.get("showView").getAsInt();
        showIndex = _buf.get("showIndex").getAsInt();
        iconAtlasID = _buf.get("iconAtlasID").getAsInt();
        iconName = _buf.get("iconName").getAsString();
        flyPos = _buf.get("flyPos").getAsString();
    }

    public static Function_Function deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Function_Function(_buf);
    }

    /**
     * 功能ID
     */
    public final int id;
    /**
     * 功能开启类型<br/>1、主线关卡<br/>2、爬塔 3、主线战败 4、拥有物品 100、卡牌页解锁（战败+英雄可升级）<br/>999、永久不开启
     */
    public final int unlockType;
    /**
     * 功能开启参数（对应类型1-关卡id；2-塔id）<br/>
     */
    public final String unlockArgs;
    /**
     * 同一场战斗后同时解锁的功能与其他那些功能互斥
     */
    public final java.util.ArrayList<Integer> afterBattleMutex;
    /**
     * 名称多语言ID
     */
    public final String nameID;
    /**
     * 说明文本多语言ID
     */
    public final String desID;
    /**
     * 是否显示开启界面：<br/>0：不显示界面<br/>1：显示开启界面
     */
    public final int showView;
    /**
     * 多项同时开启，<br/>功能显示顺序<br/>越小越优先
     */
    public final int showIndex;
    /**
     * 功能图标所在图集
     */
    public final int iconAtlasID;
    /**
     * 功能图标名称
     */
    public final String iconName;
    /**
     * 功能开启后图标飞向哪里
     */
    public final String flyPos;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "unlockType:" + unlockType + ","
        + "unlockArgs:" + unlockArgs + ","
        + "afterBattleMutex:" + afterBattleMutex + ","
        + "nameID:" + nameID + ","
        + "desID:" + desID + ","
        + "showView:" + showView + ","
        + "showIndex:" + showIndex + ","
        + "iconAtlasID:" + iconAtlasID + ","
        + "iconName:" + iconName + ","
        + "flyPos:" + flyPos + ","
        + "}";
    }
}

