
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IntegralShop_goods {
    public IntegralShop_goods(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        TypeId = _buf.get("TypeId").getAsInt();
        GroupId = _buf.get("GroupId").getAsInt();
        WeightInGroup = _buf.get("WeightInGroup").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Items").getAsJsonArray(); Items = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Items.add(_v0); }   }
        Price = _buf.get("Price").getAsInt();
        Sort = _buf.get("Sort").getAsInt();
        LevelRequirements = _buf.get("LevelRequirements").getAsInt();
        Discount = _buf.get("Discount").getAsInt();
        Hide = _buf.get("Hide").getAsInt();
    }

    public static IntegralShop_goods deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IntegralShop_goods(_buf);
    }

    /**
     * id
     */
    public final int ID;
    /**
     * 归属商店
     */
    public final int TypeId;
    /**
     * 归属组
     */
    public final int GroupId;
    /**
     * 组内权重（商店组内权重不能填0，否则服务器挂）
     */
    public final int WeightInGroup;
    /**
     * 货物参数<br/>（道具ID/单次数量）
     */
    public final java.util.ArrayList<Integer> Items;
    /**
     * 定价<br/>（原价/单次）
     */
    public final int Price;
    /**
     * 排序权重<br/>（小的在前）
     */
    public final int Sort;
    /**
     * 关卡控制<br/>（通关主线章节）
     */
    public final int LevelRequirements;
    /**
     * 折扣<br/>(百分比/不打折为100)
     */
    public final int Discount;
    /**
     * 是否隐藏<br/>0为否
     */
    public final int Hide;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "TypeId:" + TypeId + ","
        + "GroupId:" + GroupId + ","
        + "WeightInGroup:" + WeightInGroup + ","
        + "Items:" + Items + ","
        + "Price:" + Price + ","
        + "Sort:" + Sort + ","
        + "LevelRequirements:" + LevelRequirements + ","
        + "Discount:" + Discount + ","
        + "Hide:" + Hide + ","
        + "}";
    }
}

