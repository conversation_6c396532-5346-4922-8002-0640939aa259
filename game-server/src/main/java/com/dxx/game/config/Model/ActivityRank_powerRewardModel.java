
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class ActivityRank_powerRewardModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityRank_powerReward> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.ActivityRank_powerReward> _dataList;
    
    public ActivityRank_powerRewardModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityRank_powerReward>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.ActivityRank_powerReward>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.ActivityRank_powerReward _v;
            _v = com.dxx.game.config.Bean.ActivityRank_powerReward.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityRank_powerReward> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.ActivityRank_powerReward> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.ActivityRank_powerReward get(int key) { return _dataMap.get(key); }

}
