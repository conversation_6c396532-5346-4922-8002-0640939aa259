
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Guild_guildPowerModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildPower> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.Guild_guildPower> _dataList;
    
    public Guild_guildPowerModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildPower>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Guild_guildPower>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Guild_guildPower _v;
            _v = com.dxx.game.config.Bean.Guild_guildPower.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildPower> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.Guild_guildPower> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Guild_guildPower get(int key) { return _dataMap.get(key); }

}
