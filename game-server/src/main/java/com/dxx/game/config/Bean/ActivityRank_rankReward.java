
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityRank_rankReward {
    public ActivityRank_rankReward(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Rank").getAsJsonArray(); Rank = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Rank.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        Style = _buf.get("Style").getAsInt();
        languageId = _buf.get("languageId").getAsInt();
    }

    public static ActivityRank_rankReward deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityRank_rankReward(_buf);
    }

    /**
     * Id
     */
    public final int ID;
    /**
     * 排名区间
     */
    public final java.util.ArrayList<Integer> Rank;
    /**
     * 奖励
     */
    public final java.util.ArrayList<String> Reward;
    /**
     * 展示风格（1：正常展示、2：展示为RankStar+）
     */
    public final int Style;
    /**
     * 多语言ID
     */
    public final int languageId;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Rank:" + Rank + ","
        + "Reward:" + Reward + ","
        + "Style:" + Style + ","
        + "languageId:" + languageId + ","
        + "}";
    }
}

