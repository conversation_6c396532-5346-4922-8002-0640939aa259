
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ServerList_serverList {
    public ServerList_serverList(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        desc = _buf.get("desc").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("range").getAsJsonArray(); range = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  range.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("mark").getAsJsonArray(); mark = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  mark.add(_v0); }   }
        conditionCountMax = _buf.get("conditionCountMax").getAsInt();
        conditionCountMin = _buf.get("conditionCountMin").getAsInt();
        conditionDay = _buf.get("conditionDay").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("statusNew").getAsJsonArray(); statusNew = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  statusNew.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("statusFull").getAsJsonArray(); statusFull = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  statusFull.add(_v0); }   }
        sortId = _buf.get("sortId").getAsInt();
        serverPrefix = _buf.get("serverPrefix").getAsString();
        nameId = _buf.get("nameId").getAsString();
    }

    public static ServerList_serverList deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ServerList_serverList(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 描述
     */
    public final String desc;
    /**
     * 范围
     */
    public final java.util.ArrayList<Integer> range;
    /**
     * 标记
     */
    public final java.util.ArrayList<String> mark;
    /**
     * 开服条件1-最大人数 &gt;
     */
    public final int conditionCountMax;
    /**
     * 开服条件2-最小人数 &gt;=
     */
    public final int conditionCountMin;
    /**
     * 开服条件2-天数 &gt;=
     */
    public final int conditionDay;
    /**
     * 新 &lt;天|&lt;人数
     */
    public final java.util.ArrayList<Integer> statusNew;
    /**
     * 爆满 &gt;=天|&gt;=人数
     */
    public final java.util.ArrayList<Integer> statusFull;
    /**
     * Tab排序id<br/>0已占用，数字越小显示越靠前
     */
    public final int sortId;
    /**
     * 服务器名前缀
     */
    public final String serverPrefix;
    /**
     * 大区名称
     */
    public final String nameId;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "desc:" + desc + ","
        + "range:" + range + ","
        + "mark:" + mark + ","
        + "conditionCountMax:" + conditionCountMax + ","
        + "conditionCountMin:" + conditionCountMin + ","
        + "conditionDay:" + conditionDay + ","
        + "statusNew:" + statusNew + ","
        + "statusFull:" + statusFull + ","
        + "sortId:" + sortId + ","
        + "serverPrefix:" + serverPrefix + ","
        + "nameId:" + nameId + ","
        + "}";
    }
}

