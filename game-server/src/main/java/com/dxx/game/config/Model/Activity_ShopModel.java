
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Activity_ShopModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Shop> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.Activity_Shop> _dataList;
    
    public Activity_ShopModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Shop>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Activity_Shop>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Activity_Shop _v;
            _v = com.dxx.game.config.Bean.Activity_Shop.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.Id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Shop> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.Activity_Shop> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Activity_Shop get(int key) { return _dataMap.get(key); }

}
