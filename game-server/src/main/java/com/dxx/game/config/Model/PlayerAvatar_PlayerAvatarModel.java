
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class PlayerAvatar_PlayerAvatarModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar> _dataList;
    
    public PlayerAvatar_PlayerAvatarModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar _v;
            _v = com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar get(int key) { return _dataMap.get(key); }

}
