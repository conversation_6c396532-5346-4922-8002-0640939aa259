
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_MonthCard {
    public IAP_MonthCard(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        Power1 = _buf.get("Power1").getAsString();
        Power2 = _buf.get("Power2").getAsString();
        Duration = _buf.get("Duration").getAsInt();
        AlarmClock = _buf.get("AlarmClock").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("PostID").getAsJsonArray(); PostID = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  PostID.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Products").getAsJsonArray(); Products = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Products.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("ProductsPerDay").getAsJsonArray(); ProductsPerDay = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  ProductsPerDay.add(_v0); }   }
    }

    public static IAP_MonthCard deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_MonthCard(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 权限1：额外增加挂机获得的金币奖励
     */
    public final String Power1;
    /**
     * 权限2：额外增加挂机时长（s）
     */
    public final String Power2;
    /**
     * 持续时间<br/>（单位只能是天）
     */
    public final int Duration;
    /**
     * 剩余时间到达这个点时，通过邮件给玩家发出续费提示<br/>（单位只能是天）
     */
    public final int AlarmClock;
    /**
     * 续费提示邮件ID(0是测试，1是正式)
     */
    public final java.util.ArrayList<String> PostID;
    /**
     * 当即奖励<br/>itemid,count,showCount|itemid,count,,showCount
     */
    public final java.util.ArrayList<String> Products;
    /**
     * 每日奖励<br/>itemid,count,showCount|itemid,count,,showCount
     */
    public final java.util.ArrayList<String> ProductsPerDay;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "Power1:" + Power1 + ","
        + "Power2:" + Power2 + ","
        + "Duration:" + Duration + ","
        + "AlarmClock:" + AlarmClock + ","
        + "PostID:" + PostID + ","
        + "Products:" + Products + ","
        + "ProductsPerDay:" + ProductsPerDay + ","
        + "}";
    }
}

