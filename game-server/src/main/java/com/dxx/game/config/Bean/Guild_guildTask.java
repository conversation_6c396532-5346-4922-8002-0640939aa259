
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildTask {
    public Guild_guildTask(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Type = _buf.get("Type").getAsInt();
        IsInit = _buf.get("IsInit").getAsInt();
        Child = _buf.get("Child").getAsInt();
        Weight = _buf.get("Weight").getAsInt();
        AccumulationType = _buf.get("AccumulationType").getAsInt();
        Need = _buf.get("Need").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("OtherReward").getAsJsonArray(); OtherReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  OtherReward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Condition").getAsJsonArray(); Condition = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Condition.add(_v0); }   }
        languageId = _buf.get("languageId").getAsString();
    }

    public static Guild_guildTask deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildTask(_buf);
    }

    /**
     * 任务ID
     */
    public final int ID;
    /**
     * 任务类型<br/>1.登录<br/>2.公会签到
     */
    public final int Type;
    /**
     * 是否是初始任务ID<br/>(每次刷新只随机此值=1的配置)
     */
    public final int IsInit;
    /**
     * 下一级任务ID<br/>(没有就是0)
     */
    public final int Child;
    /**
     * 随机权重
     */
    public final int Weight;
    /**
     * 是否为累加条件<br/>0,非累加<br/>1,累加
     */
    public final int AccumulationType;
    /**
     * 任务进度目标
     */
    public final int Need;
    /**
     * 任务奖励
     */
    public final java.util.ArrayList<String> Reward;
    /**
     * 其他奖励<br/>客户端不需要展示
     */
    public final java.util.ArrayList<String> OtherReward;
    /**
     * 刷新条件(暂定章节进度)<br/>min|max
     */
    public final java.util.ArrayList<Integer> Condition;
    /**
     * 多语言ID
     */
    public final String languageId;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Type:" + Type + ","
        + "IsInit:" + IsInit + ","
        + "Child:" + Child + ","
        + "Weight:" + Weight + ","
        + "AccumulationType:" + AccumulationType + ","
        + "Need:" + Need + ","
        + "Reward:" + Reward + ","
        + "OtherReward:" + OtherReward + ","
        + "Condition:" + Condition + ","
        + "languageId:" + languageId + ","
        + "}";
    }
}

