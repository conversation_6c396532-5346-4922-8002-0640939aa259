
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class ActivityPower_PowerBaseModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityPower_PowerBase> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.ActivityPower_PowerBase> _dataList;
    
    public ActivityPower_PowerBaseModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityPower_PowerBase>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.ActivityPower_PowerBase>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.ActivityPower_PowerBase _v;
            _v = com.dxx.game.config.Bean.ActivityPower_PowerBase.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityPower_PowerBase> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.ActivityPower_PowerBase> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.ActivityPower_PowerBase get(int key) { return _dataMap.get(key); }

}
