
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildLevel {
    public Guild_guildLevel(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Exp = _buf.get("Exp").getAsInt();
        MaxMemberCount = _buf.get("MaxMemberCount").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("MaxPositionCount").getAsJsonArray(); MaxPositionCount = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  MaxPositionCount.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("ShopItemCount").getAsJsonArray(); ShopItemCount = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  ShopItemCount.add(_v0); }   }
        TaskCount = _buf.get("TaskCount").getAsInt();
        GuildBossOpen = _buf.get("GuildBossOpen").getAsInt();
    }

    public static Guild_guildLevel deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildLevel(_buf);
    }

    /**
     * 等级
     */
    public final int ID;
    /**
     * 升到下一级需要的经验
     */
    public final int Exp;
    /**
     * 最大人数
     */
    public final int MaxMemberCount;
    /**
     * 职位最大人数<br/>副会长|管理者
     */
    public final java.util.ArrayList<Integer> MaxPositionCount;
    /**
     * 商店出售货物数量<br/>每日|每周
     */
    public final java.util.ArrayList<Integer> ShopItemCount;
    /**
     * 公会任务数量
     */
    public final int TaskCount;
    /**
     * 是否开启公会boss
     */
    public final int GuildBossOpen;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Exp:" + Exp + ","
        + "MaxMemberCount:" + MaxMemberCount + ","
        + "MaxPositionCount:" + MaxPositionCount + ","
        + "ShopItemCount:" + ShopItemCount + ","
        + "TaskCount:" + TaskCount + ","
        + "GuildBossOpen:" + GuildBossOpen + ","
        + "}";
    }
}

