
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Item_ItemModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Item_Item> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.Item_Item> _dataList;
    
    public Item_ItemModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Item_Item>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Item_Item>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Item_Item _v;
            _v = com.dxx.game.config.Bean.Item_Item.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Item_Item> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.Item_Item> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Item_Item get(int key) { return _dataMap.get(key); }

}
