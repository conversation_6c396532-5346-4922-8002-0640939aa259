
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class MainLevelReward_MainLevelChest {
    public MainLevelReward_MainLevelChest(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        level = _buf.get("level").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Reward.add(_v0); }   }
    }

    public static MainLevelReward_MainLevelChest deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.MainLevelReward_MainLevelChest(_buf);
    }

    /**
     * 内容ID
     */
    public final int id;
    /**
     * 关卡层数
     */
    public final int level;
    /**
     * 阶段奖励
     */
    public final java.util.ArrayList<Integer> Reward;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "level:" + level + ","
        + "Reward:" + Reward + ","
        + "}";
    }
}

