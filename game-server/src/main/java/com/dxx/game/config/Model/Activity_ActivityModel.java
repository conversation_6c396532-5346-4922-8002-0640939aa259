
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Activity_ActivityModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Activity> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.Activity_Activity> _dataList;
    
    public Activity_ActivityModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Activity>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Activity_Activity>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Activity_Activity _v;
            _v = com.dxx.game.config.Bean.Activity_Activity.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.Id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Activity> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.Activity_Activity> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Activity_Activity get(int key) { return _dataMap.get(key); }

}
