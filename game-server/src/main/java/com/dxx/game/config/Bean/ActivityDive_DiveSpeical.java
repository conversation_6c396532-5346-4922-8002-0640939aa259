
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityDive_DiveSpeical {
    public ActivityDive_DiveSpeical(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Parameter").getAsJsonArray(); Parameter = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Parameter.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("reward").getAsJsonArray(); reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  reward.add(_v0); }   }
        number = _buf.get("number").getAsInt();
    }

    public static ActivityDive_DiveSpeical deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityDive_DiveSpeical(_buf);
    }

    /**
     * ID<br/>1-水母道具<br/>2-手电筒道具<br/>3-炸弹道具<br/>4-连续奖励格<br/>5-海藻格子<br/>6-鲨鱼格子<br/>7-贝壳A<br/>8-贝壳B<br/>9-特殊奖励1
     */
    public final int ID;
    /**
     * 参数<br/>4连续奖励格：几个奖励，权重|<br/>5海藻格：几个奖励，权重|<br/>6鲨鱼格：鲨鱼血量
     */
    public final java.util.ArrayList<String> Parameter;
    /**
     * 奖励池<br/>道具ID,道具数量,权重|…
     */
    public final java.util.ArrayList<String> reward;
    /**
     * 权重
     */
    public final int number;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Parameter:" + Parameter + ","
        + "reward:" + reward + ","
        + "number:" + number + ","
        + "}";
    }
}

