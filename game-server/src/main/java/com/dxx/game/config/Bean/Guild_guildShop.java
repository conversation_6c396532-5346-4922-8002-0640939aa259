
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildShop {
    public Guild_guildShop(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        GuildLevel = _buf.get("GuildLevel").getAsInt();
        Type = _buf.get("Type").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Condition").getAsJsonArray(); Condition = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Condition.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Price").getAsJsonArray(); Price = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Price.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<java.util.ArrayList<Integer>>(_json0_.size()); for(JsonElement _e0 : _json0_) { java.util.ArrayList<Integer> _v0;  { com.google.gson.JsonArray _json1_ = _e0.getAsJsonArray(); _v0 = new java.util.ArrayList<Integer>(_json1_.size()); for(JsonElement _e1 : _json1_) { int _v1;  _v1 = _e1.getAsInt();  _v0.add(_v1); }   }  Reward.add(_v0); }   }
        Limit = _buf.get("Limit").getAsInt();
        FreeCnt = _buf.get("FreeCnt").getAsInt();
        Weight = _buf.get("Weight").getAsInt();
        Position = _buf.get("Position").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Discount").getAsJsonArray(); Discount = new java.util.ArrayList<java.util.ArrayList<Integer>>(_json0_.size()); for(JsonElement _e0 : _json0_) { java.util.ArrayList<Integer> _v0;  { com.google.gson.JsonArray _json1_ = _e0.getAsJsonArray(); _v0 = new java.util.ArrayList<Integer>(_json1_.size()); for(JsonElement _e1 : _json1_) { int _v1;  _v1 = _e1.getAsInt();  _v0.add(_v1); }   }  Discount.add(_v0); }   }
        bgColor = _buf.get("bgColor").getAsInt();
    }

    public static Guild_guildShop deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildShop(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 公会等级
     */
    public final int GuildLevel;
    /**
     * 类型<br/>1.每日<br/>2.每周
     */
    public final int Type;
    /**
     * 用户刷新条件(暂定章节)
     */
    public final java.util.ArrayList<Integer> Condition;
    /**
     * 价格类型<br/>道具ID|道具数量<br/>空代表免费
     */
    public final java.util.ArrayList<Integer> Price;
    /**
     * 奖励
     */
    public final java.util.ArrayList<java.util.ArrayList<Integer>> Reward;
    /**
     * 可购买次数
     */
    public final int Limit;
    /**
     * 完全免费次数(不需要看广告)
     */
    public final int FreeCnt;
    /**
     * 权重
     */
    public final int Weight;
    /**
     * 位置
     */
    public final int Position;
    /**
     * 折扣
     */
    public final java.util.List<java.util.List<Integer>> Discount;
    /**
     * 商品底框颜色
     */
    public final int bgColor;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "GuildLevel:" + GuildLevel + ","
        + "Type:" + Type + ","
        + "Condition:" + Condition + ","
        + "Price:" + Price + ","
        + "Reward:" + Reward + ","
        + "Limit:" + Limit + ","
        + "FreeCnt:" + FreeCnt + ","
        + "Weight:" + Weight + ","
        + "Position:" + Position + ","
        + "Discount:" + Discount + ","
        + "bgColor:" + bgColor + ","
        + "}";
    }
}

