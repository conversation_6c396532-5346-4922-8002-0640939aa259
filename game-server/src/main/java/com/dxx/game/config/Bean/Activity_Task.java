
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Activity_Task {
    public Activity_Task(JsonObject _buf) { 
        Id = _buf.get("Id").getAsInt();
        GroupType = _buf.get("GroupType").getAsInt();
        LableType = _buf.get("LableType").getAsInt();
        TaskType = _buf.get("TaskType").getAsInt();
        CountType = _buf.get("CountType").getAsInt();
        Need = _buf.get("Need").getAsInt();
        Describe = _buf.get("Describe").getAsString();
        ProgressType = _buf.get("ProgressType").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Param").getAsJsonArray(); Param = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Param.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        Jump = _buf.get("Jump").getAsInt();
        UnlockNeed = _buf.get("UnlockNeed").getAsInt();
    }

    public static Activity_Task deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Activity_Task(_buf);
    }

    /**
     * 任务id
     */
    public final int Id;
    /**
     * 任务组类型
     */
    public final int GroupType;
    /**
     * 任务标签组<br/>（1日常2挑战）
     */
    public final int LableType;
    /**
     * 任务类型<br/>1：登录<br/>2：黑市购买
     */
    public final int TaskType;
    /**
     * 计数类型<br/>0:非累加<br/>1:累加
     */
    public final int CountType;
    /**
     * 完成要求
     */
    public final int Need;
    /**
     * 多语言ID
     */
    public final String Describe;
    /**
     * 0:显示0/1<br/>1:直接显示配置数
     */
    public final int ProgressType;
    /**
     * 任务参数
     */
    public final java.util.ArrayList<Integer> Param;
    /**
     * 奖励
     */
    public final java.util.ArrayList<String> Reward;
    /**
     * 跳转
     */
    public final int Jump;
    /**
     * Function中ID<br/>0为默认解锁<br/>-1为不开放
     */
    public final int UnlockNeed;

    

    @Override
    public String toString() {
        return "{ "
        + "Id:" + Id + ","
        + "GroupType:" + GroupType + ","
        + "LableType:" + LableType + ","
        + "TaskType:" + TaskType + ","
        + "CountType:" + CountType + ","
        + "Need:" + Need + ","
        + "Describe:" + Describe + ","
        + "ProgressType:" + ProgressType + ","
        + "Param:" + Param + ","
        + "Reward:" + Reward + ","
        + "Jump:" + Jump + ","
        + "UnlockNeed:" + UnlockNeed + ","
        + "}";
    }
}

