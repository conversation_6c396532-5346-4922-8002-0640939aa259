
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityFlipCard_FlipDynamicLayer {
    public ActivityFlipCard_FlipDynamicLayer(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("ComsumRange").getAsJsonArray(); ComsumRange = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  ComsumRange.add(_v0); }   }
        MinClue = _buf.get("MinClue").getAsInt();
        MaxClue = _buf.get("MaxClue").getAsInt();
        MinGrid = _buf.get("MinGrid").getAsInt();
        MaxGrid1 = _buf.get("MaxGrid1").getAsInt();
        MaxGrid2 = _buf.get("MaxGrid2").getAsInt();
    }

    public static ActivityFlipCard_FlipDynamicLayer deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityFlipCard_FlipDynamicLayer(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 累计消耗步数范围
     */
    public final java.util.ArrayList<Integer> ComsumRange;
    /**
     * 最少线索
     */
    public final int MinClue;
    /**
     * 最多线索
     */
    public final int MaxClue;
    /**
     * 最少石头
     */
    public final int MinGrid;
    /**
     * 过多石头
     */
    public final int MaxGrid1;
    /**
     * 最多石头
     */
    public final int MaxGrid2;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "ComsumRange:" + ComsumRange + ","
        + "MinClue:" + MinClue + ","
        + "MaxClue:" + MaxClue + ","
        + "MinGrid:" + MinGrid + ","
        + "MaxGrid1:" + MaxGrid1 + ","
        + "MaxGrid2:" + MaxGrid2 + ","
        + "}";
    }
}

