
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Guild_guildLevelModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildLevel> _dataMap;
    private final java.util.ArrayList<com.dxx.game.config.Bean.Guild_guildLevel> _dataList;
    
    public Guild_guildLevelModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildLevel>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Guild_guildLevel>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Guild_guildLevel _v;
            _v = com.dxx.game.config.Bean.Guild_guildLevel.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildLevel> getDataMap() { return _dataMap; }
    public java.util.ArrayList<com.dxx.game.config.Bean.Guild_guildLevel> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Guild_guildLevel get(int key) { return _dataMap.get(key); }

}
