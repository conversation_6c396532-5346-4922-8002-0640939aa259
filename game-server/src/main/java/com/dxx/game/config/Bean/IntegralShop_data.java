
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IntegralShop_data {
    public IntegralShop_data(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        NameID = _buf.get("NameID").getAsString();
        currencyID = _buf.get("currencyID").getAsInt();
        LevelRequirements = _buf.get("LevelRequirements").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("RefreshCost").getAsJsonArray(); RefreshCost = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  RefreshCost.add(_v0); }   }
    }

    public static IntegralShop_data deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IntegralShop_data(_buf);
    }

    /**
     * id
     */
    public final int ID;
    /**
     * 商店名称多语言
     */
    public final String NameID;
    /**
     * 货币类型
     */
    public final int currencyID;
    /**
     * 通关章节
     */
    public final int LevelRequirements;
    /**
     * 手动刷新消耗钻石
     */
    public final java.util.ArrayList<Integer> RefreshCost;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "NameID:" + NameID + ","
        + "currencyID:" + currencyID + ","
        + "LevelRequirements:" + LevelRequirements + ","
        + "RefreshCost:" + RefreshCost + ","
        + "}";
    }
}

