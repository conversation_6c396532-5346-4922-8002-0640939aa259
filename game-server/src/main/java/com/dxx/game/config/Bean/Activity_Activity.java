
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Activity_Activity {
    public Activity_Activity(JsonObject _buf) { 
        Id = _buf.get("Id").getAsInt();
        IsOpen = _buf.get("IsOpen").getAsInt();
        NoteID = _buf.get("NoteID").getAsString();
        Type = _buf.get("Type").getAsInt();
        srvType = _buf.get("srvType").getAsInt();
        SubType1 = _buf.get("SubType1").getAsInt();
        SubType2 = _buf.get("SubType2").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Param").getAsJsonArray(); Param = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Param.add(_v0); }   }
        OpenType = _buf.get("OpenType").getAsInt();
        OpenTime = _buf.get("OpenTime").getAsInt();
        DurationTime = _buf.get("DurationTime").getAsInt();
        EndTime = _buf.get("EndTime").getAsInt();
        TaskGroupType = _buf.get("TaskGroupType").getAsInt();
        ShopGroupType = _buf.get("ShopGroupType").getAsInt();
        RankGroupType = _buf.get("RankGroupType").getAsInt();
        ConsumeGroupType = _buf.get("ConsumeGroupType").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("MailTempId").getAsJsonArray(); MailTempId = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  MailTempId.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("MailRoundTempId").getAsJsonArray(); MailRoundTempId = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  MailRoundTempId.add(_v0); }   }
    }

    public static Activity_Activity deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Activity_Activity(_buf);
    }

    /**
     * 活动id
     */
    public final int Id;
    /**
     * 活动开关      0:关 1:开
     */
    public final int IsOpen;
    /**
     * 活动名称多语言
     */
    public final String NoteID;
    /**
     * 活动类型<br/>（1钓鱼2翻牌子3潜水4消耗抽饰品5消耗抽英雄6消耗抽藏品7战力排行榜）
     */
    public final int Type;
    /**
     * 活动服务器   1:本服 2:战区
     */
    public final int srvType;
    /**
     * 活动子类型1（指定活动id）
     */
    public final int SubType1;
    /**
     * 活动子类型2
     */
    public final int SubType2;
    /**
     * 活动参数
     */
    public final java.util.ArrayList<String> Param;
    /**
     * 活动开启类型  1:服务器开启
     */
    public final int OpenType;
    /**
     * 活动开启时间
     */
    public final int OpenTime;
    /**
     * 活动持续时间(天)
     */
    public final int DurationTime;
    /**
     * 活动结束时间
     */
    public final int EndTime;
    /**
     * 活动任务组类型
     */
    public final int TaskGroupType;
    /**
     * 活动兑换组类型
     */
    public final int ShopGroupType;
    /**
     * 排名奖励组类型
     */
    public final int RankGroupType;
    /**
     * 消耗组类型
     */
    public final int ConsumeGroupType;
    /**
     * 排行奖励邮件模板ID<br/>测试服|正式服
     */
    public final java.util.ArrayList<String> MailTempId;
    /**
     * 轮次励邮件模板ID<br/>测试服|正式服
     */
    public final java.util.ArrayList<String> MailRoundTempId;

    

    @Override
    public String toString() {
        return "{ "
        + "Id:" + Id + ","
        + "IsOpen:" + IsOpen + ","
        + "NoteID:" + NoteID + ","
        + "Type:" + Type + ","
        + "srvType:" + srvType + ","
        + "SubType1:" + SubType1 + ","
        + "SubType2:" + SubType2 + ","
        + "Param:" + Param + ","
        + "OpenType:" + OpenType + ","
        + "OpenTime:" + OpenTime + ","
        + "DurationTime:" + DurationTime + ","
        + "EndTime:" + EndTime + ","
        + "TaskGroupType:" + TaskGroupType + ","
        + "ShopGroupType:" + ShopGroupType + ","
        + "RankGroupType:" + RankGroupType + ","
        + "ConsumeGroupType:" + ConsumeGroupType + ","
        + "MailTempId:" + MailTempId + ","
        + "MailRoundTempId:" + MailRoundTempId + ","
        + "}";
    }
}

