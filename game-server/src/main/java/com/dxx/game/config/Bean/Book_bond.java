
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Book_bond {
    public Book_bond(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("CharacterBond").getAsJsonArray(); CharacterBond = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  CharacterBond.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("BondAttributes").getAsJsonArray(); BondAttributes = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  BondAttributes.add(_v0); }   }
        TitleLanguage = _buf.get("TitleLanguage").getAsInt();
        DescriptionLanguage = _buf.get("DescriptionLanguage").getAsInt();
    }

    public static Book_bond deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Book_bond(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 角色id
     */
    public final java.util.ArrayList<Integer> CharacterBond;
    /**
     * 属性加成(星数,属性|)
     */
    public final java.util.ArrayList<String> BondAttributes;
    /**
     * 标题多语言
     */
    public final int TitleLanguage;
    /**
     * 描述多语言
     */
    public final int DescriptionLanguage;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "CharacterBond:" + CharacterBond + ","
        + "BondAttributes:" + BondAttributes + ","
        + "TitleLanguage:" + TitleLanguage + ","
        + "DescriptionLanguage:" + DescriptionLanguage + ","
        + "}";
    }
}

