
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Item_Item {
    public Item_Item(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        atlasID = _buf.get("atlasID").getAsInt();
        icon = _buf.get("icon").getAsString();
        smallicon = _buf.get("smallicon").getAsString();
        quality = _buf.get("quality").getAsInt();
        inPackage = _buf.get("inPackage").getAsInt();
        inPackageWeight = _buf.get("inPackageWeight").getAsInt();
        stackable = _buf.get("stackable").getAsInt();
        nameID = _buf.get("nameID").getAsString();
        describeID = _buf.get("describeID").getAsString();
        typeDescribeID = _buf.get("typeDescribeID").getAsString();
        autoUse = _buf.get("autoUse").getAsInt();
        itemType = _buf.get("itemType").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("itemTypeParam").getAsJsonArray(); itemTypeParam = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  itemTypeParam.add(_v0); }   }
        itemGiftId = _buf.get("itemGiftId").getAsInt();
        propType = _buf.get("propType").getAsInt();
        subType = _buf.get("subType").getAsInt();
    }

    public static Item_Item deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Item_Item(_buf);
    }

    /**
     * 必须有的ID
     */
    public final int id;
    /**
     * 图集名称
     */
    public final int atlasID;
    /**
     * 图标
     */
    public final String icon;
    /**
     * 小图标（部分道具需要）
     */
    public final String smallicon;
    /**
     * 物品品质
     */
    public final int quality;
    /**
     * 是否在背包
     */
    public final int inPackage;
    /**
     * 在背包中的排序权重（权重从小到大，权重相同按品质排，均相同按ID从小达到）
     */
    public final int inPackageWeight;
    /**
     * 是否为堆叠式道具
     */
    public final int stackable;
    /**
     * 名称
     */
    public final String nameID;
    /**
     * 描述
     */
    public final String describeID;
    /**
     * 道具展示类型多语言id
     */
    public final String typeDescribeID;
    /**
     * 1.进入背包时自动使用<br/>0.不自动使用
     */
    public final int autoUse;
    /**
     * 道具类型 <br/>0-货币：金币/钻石<br/>1-装备<br/>2-英雄<br/>3-消耗品,道具<br/>4-随机道具礼包<br/>5-自选礼包<br/>6-.按时间获取奖励的礼包<br/>7-大迷宫专用神器<br/>8-英雄碎片<br/>9-固定道具礼包<br/>10—主线roguelike玩法用道具，只对装备角色生效<br/>11—主线roguelike玩法用道具，全局生效<br/>12-文字主线积分道具，只在文字游戏中使用计分，结束清空<br/>13-遗物系统（整个遗物）<br/>14-遗物系统（遗物碎片）<br/>15-爬塔门票(删除)<br/>16-竞技场门票(删除)<br/>17-消耗活动专属道具<br/>18-七日嘉年华活跃度<br/>19-矿点次数(删除)<br/>20-皮肤<br/>21-活动钓鱼杆<br/>22-主线体力(删除)<br/>30-通用门票<br/>41-公会商店货币<br/>42-公会经验<br/>43-公会活跃度
     */
    public final int itemType;
    /**
     * 类型参数
     */
    public final java.util.ArrayList<String> itemTypeParam;
    /**
     * 奖励道具id
     */
    public final int itemGiftId;
    /**
     * 道具类型<br/>1、粉尘<br/>2、沙漏<br/>3、宝箱<br/>4、钥匙<br/>5、装备强化道具<br/>6、皮肤
     */
    public final int propType;
    /**
     * 1.保底-项链<br/>2.保底-完整藏品<br/>31-时间间隔恢复道具<br/>32-指定时间恢复道具
     */
    public final int subType;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "atlasID:" + atlasID + ","
        + "icon:" + icon + ","
        + "smallicon:" + smallicon + ","
        + "quality:" + quality + ","
        + "inPackage:" + inPackage + ","
        + "inPackageWeight:" + inPackageWeight + ","
        + "stackable:" + stackable + ","
        + "nameID:" + nameID + ","
        + "describeID:" + describeID + ","
        + "typeDescribeID:" + typeDescribeID + ","
        + "autoUse:" + autoUse + ","
        + "itemType:" + itemType + ","
        + "itemTypeParam:" + itemTypeParam + ","
        + "itemGiftId:" + itemGiftId + ","
        + "propType:" + propType + ","
        + "subType:" + subType + ","
        + "}";
    }
}

