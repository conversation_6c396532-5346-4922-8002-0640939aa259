package com.dxx.game.modules.guild.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.guild.GuildShopEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.modules.common.service.RandomService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.guild.consts.GuildShopType;
import com.dxx.game.modules.guild.service.GuildShopService;
import com.dxx.game.modules.guild.service.GuildTaskService;
import com.dxx.game.modules.guild.support.GuildSupport;
import com.dxx.game.modules.reward.model.ItemReward;
import com.dxx.game.modules.reward.model.ResourceReward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dxx.game.dto.GuildProto.*;
import jakarta.annotation.Resource;
import java.util.*;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@Slf4j
@Service
public class GuildShopServiceImpl implements GuildShopService {

    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private RandomService randomService;
    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private UserService userService;
    @Resource
    private RewardService rewardService;
    @Resource
    private GuildDao guildDao;
    @Resource
    private GuildSupport guildSupport;
    @Resource
    private GuildTaskService guildTaskService;

    @DynamoDBTransactional
    @Override
    public Result<GuildShopBuyResponse> guildShopBuyAction(GuildShopBuyRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }

        int type = params.getType();
        if (type <= 0) {
            type = 1;
        }
        int shopId = params.getShopId();

        Map<Integer, GuildUser.GuildShopModel> shopModelMap = guildUser.getDailyShop();
        if (type == 2) {
            shopModelMap = guildUser.getWeeklyShop();
        }

        if (!shopModelMap.containsKey(shopId)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        GuildUser.GuildShopModel shopModel = shopModelMap.get(shopId);
        // 验证限购次数
        if (shopModel.getCount() >= shopModel.getLimit()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        RewardResultSet costResultSet = null;
        if (shopModel.getNeedItemId() > 0) {
            // 扣除资源
            ItemReward cost = ItemReward.valueOf(shopModel.getNeedItemId(), -shopModel.getNeedItemNum());
            costResultSet = rewardService.executeReward(userId, cost);
            if (costResultSet.isFailed()) {
                return Result.Error(costResultSet.getResultCode());
            }
        }

        // 发道具
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, shopModel.getReward());
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        shopModel.setCount(shopModel.getCount() + 1);
        if (type == 1) {
            guildUserDao.updateDailyShop(guildUser);
        } else if (type == 2) {
            guildUserDao.updateWeeklyShop(guildUser);
        }

        GuildShopBuyResponse.Builder response = GuildShopBuyResponse.newBuilder();

        List<GuildTaskDto> taskDtos = guildTaskService.getGuildTaskListAfterOperation(guildUser);
        if (taskDtos != null) {
            response.addAllTasks(taskDtos);
        }

        response.setCommonData(CommonHelper.buildCommonData(costResultSet, rewardResultSet));
        response.setGuildShopDto(this.buildShopDto(shopModel, shopId));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildShopRefreshResponse> refreshAction(GuildShopRefreshRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        int type = params.getType();
        if (type <= 0 || type > 2) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        // 扣除钻石
        int costDiamonds = gameConfigManager.getGuildConfig().getGuildConstEntity(116).getTypeInt();
        if (type == 2) {
            costDiamonds = gameConfigManager.getGuildConfig().getGuildConstEntity(117).getTypeInt();
        }
        ResourceReward cost = ResourceReward.valueOf(RewardResourceType.DIAMONDS, -costDiamonds);
        RewardResultSet costResultSet = rewardService.executeReward(userId, cost);
        if (costResultSet.isFailed()) {
            return Result.Error(costResultSet.getResultCode());
        }

        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        this.generateRandomShopConfigs(type, guild, guildUser, true);
        List<GuildShopDto> guildShopDtos = this.buildShopDtoList(type, guildUser);
        GuildShopRefreshResponse.Builder response = GuildShopRefreshResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costResultSet));
        response.setType(type);
        response.addAllShopDto(guildShopDtos);
        return Result.Success(response.build());
    }



    @Override
    public void refreshShop(int type, Guild guild, GuildUser guildUser) {
        this.generateRandomShopConfigs(type, guild, guildUser, true);
    }

    @Override
    public GuildShopDto buildShopDto(GuildUser.GuildShopModel shopModel, int shopId) {
        GuildShopEntity guildShopEntity = gameConfigManager.getGuildConfig().getGuildShopEntity(shopId);
        GuildShopDto.Builder result = GuildShopDto.newBuilder();
        result.setShopId(shopId);
        result.setPosition(shopModel.getPosition());
        result.setCount(shopModel.getCount());
        result.setLimit(shopModel.getLimit());
        result.setNeedItemId(shopModel.getNeedItemId());
        result.setNeedItemCount(shopModel.getNeedItemNum());
        result.addAllRewards(CommonHelper.buildRewardDtoList(shopModel.getReward()));
        result.setDiscount(shopModel.getDiscount());
        result.setFreeCnt(guildShopEntity.getFreeCnt());
        return result.build();
    }

    @Override
    public List<GuildShopDto> buildShopDtoList(int type, GuildUser guildUser) {
        List<GuildShopDto> shopDtos = new ArrayList<>();
        Map<Integer, GuildUser.GuildShopModel> shopModelMap = guildUser.getDailyShop();
        if (type == 2) {
            shopModelMap = guildUser.getWeeklyShop();
        }

        Comparator<Map.Entry<Integer, GuildUser.GuildShopModel>> comparingByPosition =
                Comparator.comparing(entry -> entry.getValue().getPosition());

        shopModelMap.entrySet().stream()
                .sorted(comparingByPosition)
                .forEach(entry -> {
                    shopDtos.add(this.buildShopDto(entry.getValue(), entry.getKey()));
                });

//        for (Map.Entry<Integer, GuildUser.GuildShopModel> entry : list) {
//            shopDtos.add(this.buildShopDto(entry.getValue(), entry.getKey()));
//        }

        return shopDtos;
    }

    @Override
    public void checkNewShop(int type, Guild guild, GuildUser guildUser) {
        this.generateRandomShopConfigs(type, guild, guildUser, false);
    }

    private void generateRandomShopConfigs(int type, Guild guild, GuildUser guildUser, boolean isInit) {
        Map<Integer, GuildUser.GuildShopModel> guildShopModelMap = null;
        int maxCnt = 0;
        if (type == GuildShopType.SHOP_DAILY) {
            maxCnt = gameConfigManager.getGuildConfig().getGuildLevelEntity(guild.getGuildLevel()).getShopItemCount().get(0);
            guildShopModelMap = guildUser.getDailyShop();
        } else if (type == GuildShopType.SHOP_WEEKLY) {
            maxCnt = gameConfigManager.getGuildConfig().getGuildLevelEntity(guild.getGuildLevel()).getShopItemCount().get(1);
            guildShopModelMap = guildUser.getWeeklyShop();
        }
        List<Integer> existPosition = new ArrayList<>();
        if (isInit) {
            // 初始化数据
            guildShopModelMap = new HashMap<>();
        } else {
            for (Map.Entry<Integer, GuildUser.GuildShopModel> entry : guildShopModelMap.entrySet()) {
                existPosition.add(entry.getValue().getPosition());
            }
        }

        if (guildShopModelMap.size() >= maxCnt) {
            return;
        }

        long userId = guildUser.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        int chapterId = userExtend.getChapterId();
        Map<Integer, List<List<Integer>>> randConfig = new HashMap<>();
        int guildLevel = guild.getGuildLevel();

        // 整理随机库
        for (Map.Entry<Integer, GuildShopEntity> entry : gameConfigManager.getGuildConfig().getGuildShop().entrySet()) {
            if (type == entry.getValue().getType() && entry.getValue().getGuildLevel() == guildLevel
                    && chapterId >= entry.getValue().getCondition().get(0)
                    && entry.getValue().getCondition().get(1) >= chapterId
                    && !existPosition.contains(entry.getValue().getPosition())
                    && entry.getValue().getPosition() <= maxCnt) {

                if (!randConfig.containsKey(entry.getValue().getPosition())) {
                    randConfig.put(entry.getValue().getPosition(), new ArrayList<>());
                }
                List<Integer> config = new ArrayList<>();
                config.add(entry.getValue().getID());
                config.add(entry.getValue().getWeight());
                randConfig.get(entry.getValue().getPosition()).add(config);
            }
        }
        if (randConfig.isEmpty()) {
            return;
        }
        for (Map.Entry<Integer, List<List<Integer>>> entry : randConfig.entrySet()) {
            // 根据权重随机
            int randShopId = randomService.randConfig(entry.getValue()).get(0);
            GuildShopEntity shopEntity = gameConfigManager.getGuildConfig().getGuildShopEntity(randShopId);

            GuildUser.GuildShopModel model = new GuildUser.GuildShopModel();
            int discount = 10;
            if (!shopEntity.getPrice().isEmpty()) {
                discount = randomService.randConfig(shopEntity.getDiscount()).get(0);
                int needItemId = shopEntity.getPrice().get(0);
                int needItemNum = shopEntity.getPrice().get(1) * discount / 10;
                model.setNeedItemId(needItemId);
                model.setNeedItemNum(needItemNum);
            }

            model.setPosition(entry.getKey());
            model.setReward(shopEntity.getReward());
            model.setCount(0);
            model.setLimit(shopEntity.getLimit());
            model.setDiscount(discount);
            guildShopModelMap.put(randShopId, model);
        }

        if (type == GuildShopType.SHOP_DAILY) {
            guildUser.setDailyShop(guildShopModelMap);
            guildUserDao.updateDailyShop(guildUser);
        } else if (type == GuildShopType.SHOP_WEEKLY) {
            guildUser.setWeeklyShop(guildShopModelMap);
            guildUserDao.updateWeeklyShop(guildUser);
        }

    }
}













